import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from "react-router-dom";
import { Toaster } from 'react-hot-toast';
import './styles/layout.css';

// General Components
import Navbar from "./Components/Navbar.jsx";
import Hero from "./Components/Hero.jsx";
import AnimatedCardsSection from "./Components/AnimatedCardsSection.jsx";
import About from "./Components/About.jsx";
import HowItWorks from "./Components/HowItWorks.jsx";
import Testimonials from "./Components/Testimonials.jsx";
import Footer from "./Components/Footer.jsx";
import Layout from "./Components/Layout.jsx";

// Protected Route Components
import ProtectedRoute from "./components/auth/ProtectedRoute.jsx";
import { AdminRoute, CompanyRoute, StudentRoute } from "./components/auth/RoleBasedRoute.jsx";
import RoleBasedRedirect from "./components/auth/RoleBasedRedirect.jsx";

// Company Components
import CompanyDashboard from "./Components/company/CompanyDashboard.jsx";
import CreateJob from './Components/company/Createjob.jsx';
import TestManagement from "./Components/company/TestManagement.jsx";
import Aptitude from "./Components/company/Aptitude.jsx";
import Interview from "./Components/company/Interview.jsx";
import CompanyProfile from "./Components/company/Profile.jsx";
import Test from "./Components/company/test.jsx";

// Admin Components
import AdminDashboard from "./Components/admin/AdminDashboard.jsx";
import AdminJobPosts from "./Components/admin/AdminJobPosts.jsx";
import AdminCompanies from "./Components/admin/AdminCompanies.jsx";
import AdminUsers from "./Components/admin/AdminUsers.jsx";
import AdminSettings from "./Components/admin/AdminSettings.jsx";

// Student Components - Legacy
import StudentDashboard from "./Components/Dashboard/Studentdashboard.jsx";
import TestInterface from "./Components/company/components/TestInterface.jsx";
import TestResult from "./Components/Dashboard/Quiz.jsx";
import InterviewPrep from "./Components/Dashboard/interview.jsx";
import LegacyStudentProfile from "./Components/Dashboard/Profile.jsx";

// Student Components - New Layout
import StudentLayout from "./pages/student/components/StudentLayout.jsx";
import StudentHome from "./pages/student/Home.jsx";
import StudentProfile from "./pages/student/Profile.jsx";
import StudentJobs from "./pages/student/Jobs.jsx";
import StudentApplications from "./pages/student/Applications.jsx";
import StudentTests from "./pages/student/Tests.jsx";
import StudentTestDetails from "./pages/student/TestDetails.jsx";
import StudentResults from "./pages/student/Results.jsx";
import JobDetails from "./pages/student/JobDetails.jsx";
import JobsCategory from "./pages/student/JobsCategory.jsx";
import ApplicationDetails from "./pages/student/applications/ApplicationDetails.jsx";
import Courses from "./pages/student/applications/Courses.jsx";
import Certificates from "./pages/student/applications/Certificates.jsx";
import Resume from "./pages/student/Resume.jsx";

// Demo Components
import AuthDemo from "./components/demo/AuthDemo.jsx";

// Auth Pages
import RegistrationPage from "./pages/RegistationPage.jsx";
import VerifyOtp from "./pages/VerifyOtp.jsx";
import LoginPage from "./pages/LoginPage.jsx";

function LandingPage() {
  return (
    <div className="font-sans text-gray-800 bg-[#f7f8fa]">
      <Navbar />
      <main>
        <section className="min-h-[85vh] flex items-center justify-center bg-white">
          <Hero />
        </section>
        <section className="py-16 px-4 bg-white">
          <div className="max-w-7xl mx-auto bg-white">
            <AnimatedCardsSection />
          </div>
        </section>
        <section className="py-20 px-4 bg-white">
          <About />
        </section>
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <HowItWorks />
          </div>
        </section>
        <section className="py-20 px-4">
          <Testimonials dark={false} />
        </section>
      </main>
      <Footer />
    </div>
  );
}

// Component wrapper to force re-renders on location change
const AppRoutes = () => {
  const location = useLocation();

  return (
    <Routes key={location.pathname}>
        {/* Public Routes */}
        <Route path="/" element={<LandingPage />} />

        {/* Role-based dashboard redirect for authenticated users */}
        <Route path="/dashboard-redirect" element={<RoleBasedRedirect />} />

        {/* Demo Route - For testing authentication */}
        <Route path="/auth-demo" element={<AuthDemo />} />

        {/* Auth Routes - Redirect to dashboard if already logged in */}
        <Route
          path="/register"
          element={
            <ProtectedRoute requireAuth={false}>
              <RegistrationPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/verify-otp"
          element={
            <ProtectedRoute requireAuth={false}>
              <VerifyOtp />
            </ProtectedRoute>
          }
        />
        <Route
          path="/login"
          element={
            <ProtectedRoute requireAuth={false}>
              <LoginPage />
            </ProtectedRoute>
          }
        />

        {/* Admin Routes - Only accessible by admin role */}
        <Route
          path="/admin-dashboard"
          element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          }
        >
          <Route path="users" element={<AdminUsers />} />
          <Route path="companies" element={<AdminCompanies />} />
          <Route path="job-posts" element={<AdminJobPosts />} />
          <Route path="settings" element={<AdminSettings />} />
        </Route>

        {/* Legacy Admin Routes - Redirect to nested routes */}
        <Route path="/admin-dashboard/users" element={<Navigate to="/admin-dashboard/users" replace />} />
        <Route path="/admin-dashboard/companies" element={<Navigate to="/admin-dashboard/companies" replace />} />
        <Route path="/admin-dashboard/job-posts" element={<Navigate to="/admin-dashboard/job-posts" replace />} />
        <Route path="/admin-dashboard/settings" element={<Navigate to="/admin-dashboard/settings" replace />} />

        {/* Company Routes under Layout - Only accessible by company role */}
        <Route
          path="/"
          element={
            <CompanyRoute>
              <Layout />
            </CompanyRoute>
          }
        >
          <Route path="dashboard" element={<CompanyDashboard />} />
          <Route path="job-create" element={<CreateJob />} />
          <Route path="test-management" element={<TestManagement />} />
          <Route path="aptitude" element={<Aptitude />} />
          <Route path="interview" element={<Interview />} />
          <Route path="profile" element={<CompanyProfile />} />
        </Route>

        {/* Legacy Company Routes - Redirect to new structure */}
        <Route path="/dashboard/job-create" element={<Navigate to="/job-create" replace />} />
        <Route path="/dashboard/aptitude" element={<Navigate to="/aptitude" replace />} />
        <Route path="/dashboard/test" element={<Navigate to="/test-management" replace />} />
        <Route path="/dashboard/interview" element={<Navigate to="/interview" replace />} />
        <Route path="/dashboard/profile" element={<Navigate to="/profile" replace />} />

        {/* Student Routes - New Layout Structure */}
        <Route
          path="/student"
          element={
            <StudentRoute>
              <StudentLayout />
            </StudentRoute>
          }
        >
          <Route index element={<StudentHome />} />
          <Route path="home" element={<StudentHome />} />
          <Route path="profile" element={<StudentProfile />} />
          <Route path="resume" element={<Resume />} />
          <Route path="jobs" element={<StudentJobs />} />
          <Route path="jobs/category/:categoryName" element={<JobsCategory />} />
          <Route path="jobs/:id" element={<JobDetails />} />
          <Route path="applications" element={<StudentApplications />} />
          <Route path="applications/:id" element={<ApplicationDetails />} />
          <Route path="tests" element={<StudentTests />} />
          <Route path="tests/:testid" element={<StudentTestDetails />} />
          <Route path="results" element={<StudentResults />} />
          <Route path="courses" element={<Courses />} />
          <Route path="certificates" element={<Certificates />} />
        </Route>

        {/* Legacy Student Routes - Redirect to new structure */}
        <Route path="/student-dashboard" element={<Navigate to="/student/home" replace />} />
        <Route path="/student-profile" element={<Navigate to="/student/profile" replace />} />

        {/* Legacy Student Test Routes - For backward compatibility */}
        <Route
          path="/test"
          element={
            <StudentRoute>
              <TestInterface />
            </StudentRoute>
          }
        />
        <Route
          path="/test-result"
          element={
            <StudentRoute>
              <TestResult />
            </StudentRoute>
          }
        />
        <Route
          path="/interview-prep"
          element={
            <StudentRoute>
              <InterviewPrep />
            </StudentRoute>
          }
        />

        {/* Alternative Student Dashboard - Legacy Support */}
        <Route
          path="/legacy-student-dashboard"
          element={
            <StudentRoute>
              <StudentDashboard />
            </StudentRoute>
          }
        />

        {/* Catch all route - redirect to home */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
  );
};

function App() {
  return (
    <Router>
      <AppRoutes />

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
            borderRadius: '10px',
            padding: '16px',
            fontSize: '14px',
            fontWeight: '500',
          },
          success: {
            style: { background: '#10B981' },
            iconTheme: { primary: '#fff', secondary: '#10B981' },
          },
          error: {
            style: { background: '#EF4444' },
            iconTheme: { primary: '#fff', secondary: '#EF4444' },
          },
          loading: {
            style: { background: '#3B82F6' },
          },
        }}
      />
    </Router>
  );
}

export default App;