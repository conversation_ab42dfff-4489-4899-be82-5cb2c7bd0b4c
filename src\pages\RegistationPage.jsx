import React, { useState } from 'react';
import { Eye, EyeOff, User, Mail, Lock } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../store/authStore';

const RegistrationPage = () => {
    const navigate = useNavigate();
    const [formData, setFormData] = useState({ name: '', email: '', password: '' });
    const [showPassword, setShowPassword] = useState(false);
    const [focusedField, setFocusedField] = useState('');

    const { register, loading, error } = useAuthStore(); // Pull from Zustand
    const [message, setMessage] = useState('');

    const handleChange = (e) => {
        setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setMessage('');

        const result = await register(formData); // Call Zustand store register method

        if (result && result.message) {
            setMessage(result.message);

            // Navigate immediately after successful registration
            // The success message will be shown briefly before navigation
            navigate('/verify-otp', {
                state: {
                    email: formData.email,
                    message: result.message
                },
                replace: true // Use replace to ensure proper navigation
            });

            // Clear form data after navigation
            setFormData({ name: '', email: '', password: '' });
        }
    };

    return (
        <div className="min-h-screen bg-[#f7f8fa] flex items-center justify-center px-4 py-12">
            <div className="w-full max-w-md bg-white p-8 rounded-2xl shadow-xl">
                <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-[#29354d] rounded-full mb-4 shadow-lg">
                        <User className="w-8 h-8 text-white" />
                    </div>
                    <h1 className="text-3xl font-bold text-[#29354d] mb-2">Join Us Today</h1>
                    <p className="text-sm text-gray-500">Create your account and get started</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Full Name */}
                    <div>
                        <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-1">Full Name</label>
                        <div className="relative">
                            <User className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${focusedField === 'name' ? 'text-[#29354d]' : 'text-gray-400'}`} />
                            <input
                                id="name"
                                name="name"
                                type="text"
                                value={formData.name}
                                onChange={handleChange}
                                onFocus={() => setFocusedField('name')}
                                onBlur={() => setFocusedField('')}
                                required
                                placeholder="Enter your full name"
                                className="w-full border border-gray-300 bg-white pl-12 pr-4 py-2 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#29354d]"
                            />
                        </div>
                    </div>

                    {/* Email */}
                    <div>
                        <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-1">Email Address</label>
                        <div className="relative">
                            <Mail className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${focusedField === 'email' ? 'text-[#29354d]' : 'text-gray-400'}`} />
                            <input
                                id="email"
                                name="email"
                                type="email"
                                value={formData.email}
                                onChange={handleChange}
                                onFocus={() => setFocusedField('email')}
                                onBlur={() => setFocusedField('')}
                                required
                                placeholder="Enter your email"
                                className="w-full border border-gray-300 bg-white pl-12 pr-4 py-2 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#29354d]"
                            />
                        </div>
                    </div>

                    {/* Password */}
                    <div>
                        <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-1">Password</label>
                        <div className="relative">
                            <Lock className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${focusedField === 'password' ? 'text-[#29354d]' : 'text-gray-400'}`} />
                            <input
                                id="password"
                                name="password"
                                type={showPassword ? 'text' : 'password'}
                                value={formData.password}
                                onChange={handleChange}
                                onFocus={() => setFocusedField('password')}
                                onBlur={() => setFocusedField('')}
                                required
                                placeholder="Create a password"
                                className="w-full border border-gray-300 bg-white pl-12 pr-12 py-2 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#29354d]"
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-[#29354d]"
                            >
                                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                            </button>
                        </div>
                    </div>

                    {/* Submit */}
                    <button
                        type="submit"
                        disabled={loading}
                        className={`w-full text-white font-semibold py-2 px-4 rounded-lg transition ${loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#29354d] hover:bg-[#1e263a]'}`}
                    >
                        {loading ? 'Registering...' : 'Create Account'}
                    </button>
                </form>

                {/* Success/Error Message */}
                {message && (
                    <p className="mt-4 text-green-600 text-center text-sm">{message}</p>
                )}
                {error && (
                    <p className="mt-4 text-red-600 text-center text-sm">{error}</p>
                )}

                {/* Login Link */}
                <div className="mt-6 text-center text-sm text-gray-600">
                    Already have an account?{' '}
                    <a
                        href="/login"
                        className="text-[#29354d] font-semibold hover:underline"
                    >
                        Login
                    </a>
                </div>
            </div>
        </div>
    );
};

export default RegistrationPage;
