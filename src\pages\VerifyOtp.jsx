import React, { useEffect, useRef, useState } from 'react';
import { Mail, Shield, Clock, RefreshCw, CheckCircle } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import useAuthStore from '../store/authStore';

const VerifyOtp = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { verifyOtp, loading, error } = useAuthStore();

    const [formData, setFormData] = useState({
        email: location.state?.email || '',
        otp: ''
    });
    const [message, setMessage] = useState(location.state?.message || '');
    const [timer, setTimer] = useState(60);
    const [resending, setResending] = useState(false);
    const [focusedField, setFocusedField] = useState('');
    const [otpDigits, setOtpDigits] = useState(['', '', '', '', '', '']);

    const otpRefs = useRef([]);

    useEffect(() => {
        otpRefs.current[0]?.focus(); // Auto-focus first OTP field on mount
    }, []);

    useEffect(() => {
        if (timer === 0) return;
        const interval = setInterval(() => setTimer((t) => t - 1), 1000);
        return () => clearInterval(interval);
    }, [timer]);

    const handleChange = (e) => {
        setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    };

    const handleOtpChange = (index, value) => {
        if (!/^\d*$/.test(value)) return;

        const newDigits = [...otpDigits];
        newDigits[index] = value;
        setOtpDigits(newDigits);
        setFormData((prev) => ({ ...prev, otp: newDigits.join('') }));

        if (value && index < 5) otpRefs.current[index + 1]?.focus();
    };

    const handleOtpKeyDown = (index, e) => {
        if (e.key === 'Backspace' && !otpDigits[index] && index > 0) {
            otpRefs.current[index - 1]?.focus();
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setMessage('');

        const result = await verifyOtp(formData);

        if (result && result.message) {
            setMessage(result.message);

            // Navigate immediately after successful verification
            // The success message will be shown on the login page
            navigate('/login', {
                state: {
                    message: 'Email verified successfully! Please login to continue.'
                },
                replace: true // Use replace to ensure proper navigation
            });
        }
    };

    const handleResendOtp = async () => {
        setResending(true);
        setError('');
        setMessage('');
        try {
            const res = await fetch('/api/auth/resend-otp', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: formData.email }),
            });
            const data = await res.json();
            if (!res.ok) throw new Error(data.error || 'Failed to resend OTP');

            setMessage('OTP resent successfully.');
            setTimer(60);
            setOtpDigits(['', '', '', '', '', '']);
            setFormData((prev) => ({ ...prev, otp: '' }));
            otpRefs.current[0]?.focus();
        } catch (err) {
            setError(err.message);
        } finally {
            setResending(false);
        }
    };

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <div className="min-h-screen bg-[#f7f8fa] flex items-center justify-center px-4 py-12">
            <div className="w-full max-w-md bg-white p-8 rounded-2xl shadow-xl">
                <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-[#29354d] rounded-full mb-4 shadow-lg">
                        <Shield className="w-8 h-8 text-white" />
                    </div>
                    <h1 className="text-3xl font-bold text-[#29354d] mb-2">Verify Your Email</h1>
                    <p className="text-sm text-gray-500">We've sent a 6-digit otp to your email</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Email */}
                    <div>
                        <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-1">
                            Email Address
                        </label>
                        <div className="relative">
                            <Mail className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${focusedField === 'email' ? 'text-[#29354d]' : 'text-gray-400'}`} />
                            <input
                                id="email"
                                name="email"
                                type="email"
                                value={formData.email}
                                onChange={handleChange}
                                onFocus={() => setFocusedField('email')}
                                onBlur={() => setFocusedField('')}
                                required
                                className="w-full border border-gray-300 bg-white pl-12 pr-4 py-2 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#29354d]"
                                placeholder="Enter your email address"
                            />
                        </div>
                    </div>

                    {/* OTP Inputs */}
                    <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-3">Enter 6-Digit otp</label>
                        <div className="flex justify-center space-x-3 mb-4">
                            {otpDigits.map((digit, index) => (
                                <input
                                    key={index}
                                    ref={(el) => (otpRefs.current[index] = el)}
                                    type="text"
                                    maxLength={1}
                                    value={digit}
                                    onChange={(e) => handleOtpChange(index, e.target.value)}
                                    onKeyDown={(e) => handleOtpKeyDown(index, e)}
                                    className="w-12 h-14 border border-gray-300 rounded-lg text-gray-800 text-center text-xl font-bold focus:outline-none focus:ring-2 focus:ring-[#29354d] focus:border-[#29354d] transition-all"
                                />
                            ))}
                        </div>
                        <p className="text-gray-500 text-xs text-center">Enter the otp exactly as received</p>
                    </div>

                    {/* Timer and Resend */}
                    <div className="flex items-center justify-center mb-6">
                        {timer > 0 ? (
                            <div className="flex items-center space-x-2 text-gray-600">
                                <Clock className="w-4 h-4" />
                                <span className="text-sm font-medium">Resend otp in {formatTime(timer)}</span>
                            </div>
                        ) : (
                            <button
                                type="button"
                                onClick={handleResendOtp}
                                disabled={resending}
                                className="flex items-center space-x-2 text-[#29354d] hover:text-[#1e263a] transition disabled:opacity-50 disabled:cursor-not-allowed group"
                            >
                                <RefreshCw className={`w-4 h-4 ${resending ? 'animate-spin' : 'group-hover:rotate-180'} transition`} />
                                <span className="text-sm font-medium underline underline-offset-4">
                                    {resending ? 'Sending...' : 'Resend otp'}
                                </span>
                            </button>
                        )}
                    </div>

                    {/* Submit Button */}
                    <button
                        type="submit"
                        disabled={loading || formData.otp.length !== 6}
                        className={`w-full text-white font-semibold py-2 px-4 rounded-lg transition ${loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#29354d] hover:bg-[#1e263a]'}`}
                    >
                        <span className="flex items-center justify-center">
                            {loading ? (
                                <>
                                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                    Verifying...
                                </>
                            ) : (
                                <>
                                    <CheckCircle className="w-5 h-5 mr-2" />
                                    Verify otp
                                </>
                            )}
                        </span>
                    </button>
                </form>

                {/* Message Displays */}
                {message && (
                    <p className="mt-4 text-green-600 text-center text-sm">{message}</p>
                )}
                {error && (
                    <p className="mt-4 text-red-600 text-center text-sm">{error}</p>
                )}

                <p className="mt-6 text-gray-500 text-xs text-center">
                    Didn’t receive the otp? Check your spam or try resending.
                </p>
            </div>
        </div> 
    );
};

export default VerifyOtp;
